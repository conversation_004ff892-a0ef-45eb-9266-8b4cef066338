# 🔥 Firebase Setup Guide for EBP Project Registry

This guide will help you set up Firebase to make the application fully functional.

## 📋 Prerequisites

- Node.js 16+ installed
- A Google account
- Firebase CLI (we'll install this)

## 🚀 Step 1: Create Firebase Project

1. **Go to Firebase Console**
   - Visit: https://console.firebase.google.com
   - Sign in with your Google account

2. **Create New Project**
   - Click "Create a project"
   - Project name: `ebp-project-registry` (or your preferred name)
   - Enable Google Analytics: **Optional** (recommended for production)
   - Click "Create project"

## 🗄️ Step 2: Enable Firestore Database

1. **Navigate to Firestore**
   - In Firebase Console, click "Firestore Database" in the left sidebar
   - Click "Create database"

2. **Configure Security Rules**
   - Choose "Start in **test mode**" (we'll update rules later)
   - Click "Next"

3. **Choose Location**
   - Select a location close to your users
   - Click "Done"

## 🔐 Step 3: Enable Authentication

1. **Navigate to Authentication**
   - Click "Authentication" in the left sidebar
   - Click "Get started"

2. **Enable Email/Password**
   - Go to "Sign-in method" tab
   - Click "Email/Password"
   - Enable the first toggle (Email/Password)
   - Click "Save"

## ⚙️ Step 4: Get Firebase Configuration

1. **Add Web App**
   - Go to Project Settings (gear icon ⚙️)
   - Scroll down to "Your apps"
   - Click the web icon `</>`
   - App nickname: `EBP Registry Web`
   - **Don't** check "Also set up Firebase Hosting"
   - Click "Register app"

2. **Copy Configuration**
   - Copy the `firebaseConfig` object
   - It should look like this:
   ```javascript
   const firebaseConfig = {
     apiKey: "AIza...",
     authDomain: "your-project.firebaseapp.com",
     projectId: "your-project-id",
     storageBucket: "your-project.appspot.com",
     messagingSenderId: "123456789",
     appId: "1:123456789:web:abcdef"
   };
   ```

## 🔧 Step 5: Configure Your Local Environment

1. **Create Environment File**
   ```bash
   cp .env.example .env.local
   ```

2. **Edit .env.local**
   - Open `.env.local` in your editor
   - Replace the placeholder values with your actual Firebase config:
   ```env
   VITE_FIREBASE_API_KEY=AIza...
   VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your-project-id
   VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
   VITE_FIREBASE_APP_ID=1:123456789:web:abcdef
   ```

## 🛠️ Step 6: Install Firebase CLI & Deploy Rules

1. **Install Firebase CLI**
   ```bash
   npm install -g firebase-tools
   ```

2. **Login to Firebase**
   ```bash
   firebase login
   ```

3. **Initialize Firebase in Project**
   ```bash
   firebase init
   ```
   - Select: **Firestore** and **Hosting**
   - Choose: **Use an existing project**
   - Select your project from the list
   - Firestore rules file: `firestore.rules` (default)
   - Firestore indexes file: `firestore.indexes.json` (default)
   - Public directory: `dist`
   - Single-page app: **Yes**
   - Automatic builds: **No**

4. **Deploy Firestore Rules**
   ```bash
   firebase deploy --only firestore
   ```

## 👤 Step 7: Create Admin User

1. **Start Development Server**
   ```bash
   npm run dev
   ```

2. **Create Admin Account**
   - Go to http://localhost:5173/login
   - Try to login (it will fail - that's expected)
   - Go to Firebase Console > Authentication > Users
   - Click "Add user"
   - Email: `<EMAIL>` (or your preferred admin email)
   - Password: Create a secure password
   - Click "Add user"

3. **Test Login**
   - Go back to http://localhost:5173/login
   - Login with your admin credentials
   - You should be redirected to the admin dashboard

## ✅ Step 8: Test the Application

1. **Test Project Registration**
   - Go to http://localhost:5173/register
   - Fill out the form and submit
   - Check Firebase Console > Firestore to see the data

2. **Test Project List**
   - Go to http://localhost:5173/projects
   - You should see your registered project

## 🔒 Step 9: Security (Production)

When ready for production:

1. **Update Firestore Rules**
   - Edit `firestore.rules` for stricter security
   - Deploy: `firebase deploy --only firestore:rules`

2. **Set Admin Claims** (Optional)
   - Use Firebase Admin SDK to set custom claims
   - Or manage admins through the application

## 🚨 Troubleshooting

### Common Issues:

1. **"Firebase not configured" errors**
   - Check your `.env.local` file exists and has correct values
   - Restart the dev server: `npm run dev`

2. **Authentication errors**
   - Verify Email/Password is enabled in Firebase Console
   - Check your admin user exists in Authentication > Users

3. **Firestore permission errors**
   - Ensure Firestore rules are deployed
   - Check the rules allow read/write operations

4. **Build errors**
   - Clear cache: `rm -rf node_modules && npm install`
   - Check for TypeScript errors

## 🎉 You're Done!

Your Firebase setup is complete! The application should now:
- ✅ Connect to your Firebase project
- ✅ Allow admin login
- ✅ Store projects in Firestore
- ✅ Display projects from the database

Need help? Check the console for error messages or refer to the Firebase documentation.
