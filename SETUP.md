# EBP Project Central Registry - Setup Guide

This guide will help you set up the EBP Project Central Registry application from scratch.

## Prerequisites

- Node.js 16 or higher
- npm or yarn package manager
- Firebase account
- Modern web browser

## Step 1: Project Setup

The project is already initialized with all dependencies. If you need to reinstall:

```bash
npm install
```

## Step 2: Firebase Configuration

### 2.1 Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Click "Create a project"
3. Enter project name: `ebp-project-registry`
4. Enable Google Analytics (optional)
5. Create project

### 2.2 Enable Firestore Database

1. In Firebase Console, go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (we'll update rules later)
4. Select a location close to your users
5. Click "Done"

### 2.3 Enable Authentication

1. Go to "Authentication" in Firebase Console
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Save changes

### 2.4 Get Firebase Configuration

1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Click "Web" icon to add a web app
4. Register app with name: `EBP Registry`
5. Copy the Firebase configuration object

### 2.5 Update Configuration File

Edit `src/firebase/config.js` and replace the placeholder values:

```javascript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
}
```

## Step 3: Deploy Firestore Rules

1. Install Firebase CLI globally:
```bash
npm install -g firebase-tools
```

2. Login to Firebase:
```bash
firebase login
```

3. Initialize Firebase in your project:
```bash
firebase init
```
- Select "Firestore" and "Hosting"
- Choose your existing project
- Accept default files for Firestore rules and indexes
- Set public directory to `dist`
- Configure as single-page app: Yes
- Don't overwrite index.html

4. Deploy Firestore rules:
```bash
firebase deploy --only firestore
```

## Step 4: Create Admin User

1. Start the development server:
```bash
npm run dev
```

2. Open http://localhost:5173 in your browser

3. Navigate to `/login` and try to sign in (this will fail)

4. Go to Firebase Console > Authentication > Users

5. Click "Add user" and create an admin account:
   - Email: `<EMAIL>`
   - Password: Choose a secure password

6. Now you can log in to the admin dashboard

## Step 5: Test the Application

### 5.1 Add Sample Data (Development)

1. The application includes a DevTools component in development mode
2. Look for the gear icon in the bottom-right corner
3. Click "Add Sample Projects" to populate with test data
4. This will create 5 sample projects with different statuses and types

### 5.2 Test Core Features

1. **Project Registration**:
   - Go to `/register`
   - Fill out the form with test data
   - Submit and verify it appears in the project list

2. **Project List**:
   - Go to `/projects`
   - Test filtering by department, status, and type
   - Test search functionality
   - Test sorting by clicking column headers

3. **Project Actions** (requires employee verification):
   - Click "Edit" on a project
   - Enter the project leader's employee number (from sample data)
   - Verify the edit form opens
   - Test adding/removing collaborators
   - Test leadership transfer

4. **Admin Dashboard**:
   - Log in as admin at `/login`
   - Go to `/admin`
   - Test configuration management
   - Test project export functionality

## Step 6: Production Deployment

### 6.1 Build for Production

```bash
npm run build
```

### 6.2 Deploy to Firebase Hosting

```bash
firebase deploy --only hosting
```

### 6.3 Update Firestore Rules for Production

Edit `firestore.rules` to be more restrictive if needed, then deploy:

```bash
firebase deploy --only firestore:rules
```

## Security Configuration

### Admin Users

To make a user an admin:

1. Go to Firebase Console > Authentication
2. Find the user and note their UID
3. Use Firebase Admin SDK or Firebase CLI to set custom claims:

```javascript
// Using Firebase Admin SDK
admin.auth().setCustomUserClaims(uid, { admin: true })
```

### Employee Verification

The application uses employee number verification for project mutations:
- Project leaders must enter their 6-digit employee number
- This is verified against the stored project leader data
- No external HR system integration is included (can be added later)

## Troubleshooting

### Common Issues

1. **Firebase connection errors**:
   - Verify your Firebase config is correct
   - Check that Firestore is enabled
   - Ensure your project ID matches

2. **Authentication issues**:
   - Verify Email/Password provider is enabled
   - Check that admin user exists in Firebase Auth

3. **Build errors**:
   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`
   - Check for TypeScript errors: `npm run type-check`

4. **Styling issues**:
   - Verify Tailwind CSS is properly configured
   - Check that PrimeVue theme is loading

### Development Tips

1. Use the DevTools component to quickly add test data
2. Check browser console for any JavaScript errors
3. Use Firebase Console to monitor Firestore operations
4. Test on different screen sizes for responsive design

## Next Steps

1. Customize the department and rank lists in the admin dashboard
2. Add your organization's branding and colors
3. Configure email notifications (requires additional Firebase setup)
4. Integrate with your HR system for employee verification
5. Add more detailed reporting and analytics
6. Implement data backup and recovery procedures

## Support

For technical issues:
1. Check the browser console for errors
2. Review Firebase Console for authentication/database issues
3. Verify all dependencies are properly installed
4. Test with sample data first before adding real projects
