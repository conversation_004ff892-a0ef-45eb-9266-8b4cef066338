// Sample data for testing the application
export const sampleProjects = [
  {
    department: 'Emergency Medicine',
    projectTitle: 'Implementation of Rapid Triage Protocol for Chest Pain Patients',
    projectLeader: {
      rank: 'Consultant',
      fullName: 'Dr. <PERSON>',
      employeeNumber: '123456'
    },
    collaborators: [
      {
        rank: 'Senior Registrar',
        fullName: 'Dr. <PERSON>',
        employeeN<PERSON>ber: '234567'
      },
      {
        rank: 'Senior Nurse',
        fullName: 'Nurse <PERSON>',
        employeeNumber: '345678'
      }
    ],
    progressStatus: 'In Progress',
    projectType: 'EBP',
    commencementDate: new Date('2024-01-15'),
    completionDate: new Date('2024-06-30'),
    projectDetails: `# Project Overview
This evidence-based practice project aims to implement a rapid triage protocol for chest pain patients in the emergency department.

## Objectives
- Reduce door-to-ECG time to under 10 minutes
- Improve patient satisfaction scores
- Decrease length of stay for low-risk chest pain patients

## Methodology
- Literature review of current best practices
- Staff training on new protocol
- Pilot implementation with selected shifts
- Data collection and analysis`
  },
  {
    department: 'Internal Medicine',
    projectTitle: 'Medication Reconciliation Process Improvement',
    projectLeader: {
      rank: 'Senior Registrar',
      fullName: 'Dr. <PERSON>',
      employee<PERSON><PERSON>ber: '456789'
    },
    collaborators: [
      {
        rank: 'Pharmacist',
        fullName: 'PharmD <PERSON>',
        employeeNumber: '567890'
      }
    ],
    progressStatus: 'Completed',
    projectType: 'CQI Project',
    commencementDate: new Date('2023-09-01'),
    completionDate: new Date('2024-02-28'),
    projectDetails: `# Medication Reconciliation Improvement

## Background
High rates of medication errors during transitions of care.

## Interventions
1. Electronic medication reconciliation tool
2. Pharmacist-led medication reviews
3. Patient education materials

## Results
- 40% reduction in medication discrepancies
- Improved patient safety scores
- Enhanced interdisciplinary collaboration`
  },
  {
    department: 'Surgery',
    projectTitle: 'Enhanced Recovery After Surgery (ERAS) Protocol Implementation',
    projectLeader: {
      rank: 'Consultant',
      fullName: 'Dr. Robert Martinez',
      employeeNumber: '678901'
    },
    collaborators: [
      {
        rank: 'Registrar',
        fullName: 'Dr. Emily Davis',
        employeeNumber: '789012'
      },
      {
        rank: 'Senior Nurse',
        fullName: 'Nurse Manager Jennifer Lee',
        employeeNumber: '890123'
      },
      {
        rank: 'Pharmacist',
        fullName: 'PharmD David Wilson',
        employeeNumber: '901234'
      }
    ],
    progressStatus: 'Not Started',
    projectType: 'EBP',
    commencementDate: new Date('2024-04-01'),
    completionDate: new Date('2024-12-31'),
    projectDetails: `# ERAS Protocol Implementation

## Purpose
Implement evidence-based Enhanced Recovery After Surgery protocols to improve patient outcomes and reduce length of stay.

## Target Procedures
- Colorectal surgery
- Orthopedic joint replacements
- Gynecologic procedures

## Expected Outcomes
- Reduced length of stay by 1-2 days
- Decreased postoperative complications
- Improved patient satisfaction
- Cost savings for the institution`
  },
  {
    department: 'Pediatrics',
    projectTitle: 'Family-Centered Rounds Implementation',
    projectLeader: {
      rank: 'Medical Officer',
      fullName: 'Dr. Maria Rodriguez',
      employeeNumber: '012345'
    },
    collaborators: [],
    progressStatus: 'In Progress',
    projectType: 'Research',
    commencementDate: new Date('2024-02-01'),
    completionDate: new Date('2024-08-31'),
    projectDetails: `# Family-Centered Rounds Research Project

## Objective
Evaluate the impact of family-centered rounds on patient satisfaction, family engagement, and clinical outcomes in the pediatric ward.

## Study Design
- Randomized controlled trial
- Pre/post implementation comparison
- Mixed-methods approach

## Measures
- Family satisfaction surveys
- Length of stay
- Readmission rates
- Staff satisfaction`
  },
  {
    department: 'Nursing',
    projectTitle: 'Pressure Ulcer Prevention Bundle',
    projectLeader: {
      rank: 'Senior Nurse',
      fullName: 'Nurse Manager Susan Brown',
      employeeNumber: '123789'
    },
    collaborators: [
      {
        rank: 'Staff Nurse',
        fullName: 'Nurse Kelly Anderson',
        employeeNumber: '234890'
      },
      {
        rank: 'Staff Nurse',
        fullName: 'Nurse James Taylor',
        employeeNumber: '345901'
      }
    ],
    progressStatus: 'Completed',
    projectType: 'EBP',
    commencementDate: new Date('2023-06-01'),
    completionDate: new Date('2023-12-31'),
    projectDetails: `# Pressure Ulcer Prevention Bundle

## Implementation
Comprehensive pressure ulcer prevention bundle including:
- Risk assessment tools
- Repositioning protocols
- Skin care guidelines
- Staff education program

## Results
- 60% reduction in hospital-acquired pressure ulcers
- Improved compliance with prevention protocols
- Enhanced patient comfort and safety`
  }
]

// Function to add sample data to Firestore (for development/testing)
export async function addSampleData(projectsStore) {
  try {
    console.log('Adding sample data...')
    
    for (const project of sampleProjects) {
      await projectsStore.addProject(project)
      console.log(`Added project: ${project.projectTitle}`)
    }
    
    console.log('Sample data added successfully!')
    return true
  } catch (error) {
    console.error('Error adding sample data:', error)
    return false
  }
}
