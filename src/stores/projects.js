import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import {
  collection,
  addDoc,
  getDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  // where,
  serverTimestamp,
} from 'firebase/firestore'
import { db } from '@/firebase/config'

export const useProjectsStore = defineStore('projects', () => {
  const projects = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Computed
  const projectCount = computed(() => projects.value.length)
  const completedProjects = computed(() =>
    projects.value.filter((p) => p.progressStatus === 'Completed'),
  )
  const inProgressProjects = computed(() =>
    projects.value.filter((p) => p.progressStatus === 'In Progress'),
  )

  // Actions
  async function fetchProjects() {
    loading.value = true
    error.value = null
    try {
      const q = query(collection(db, 'projects'), orderBy('createdAt', 'desc'))
      const querySnapshot = await getDocs(q)
      projects.value = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }))
    } catch (err) {
      error.value = err.message
      console.error('Error fetching projects:', err)
    } finally {
      loading.value = false
    }
  }

  async function addProject(projectData) {
    loading.value = true
    error.value = null
    try {
      const docRef = await addDoc(collection(db, 'projects'), {
        ...projectData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      // Add to local state
      const newProject = {
        id: docRef.id,
        ...projectData,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      projects.value.unshift(newProject)

      return docRef.id
    } catch (err) {
      error.value = err.message
      console.error('Error adding project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function updateProject(projectId, updates) {
    loading.value = true
    error.value = null
    try {
      const projectRef = doc(db, 'projects', projectId)
      await updateDoc(projectRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      })

      // Update local state
      const index = projects.value.findIndex((p) => p.id === projectId)
      if (index !== -1) {
        projects.value[index] = {
          ...projects.value[index],
          ...updates,
          updatedAt: new Date(),
        }
      }
    } catch (err) {
      error.value = err.message
      console.error('Error updating project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function getProjectById(projectId) {
    loading.value = true
    error.value = null
    try {
      const projectRef = doc(db, 'projects', projectId)
      const projectSnapshot = await getDoc(projectRef)
      if (projectSnapshot.exists()) {
        return projectSnapshot.data()
      } else {
        console.log('Project not found')
        return null
      }
    } catch (err) {
      error.value = err.message
      console.error('Error fetching project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function deleteProject(projectId) {
    loading.value = true
    error.value = null
    try {
      await deleteDoc(doc(db, 'projects', projectId))

      // Remove from local state
      projects.value = projects.value.filter((p) => p.id !== projectId)
    } catch (err) {
      error.value = err.message
      console.error('Error deleting project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function verifyProjectLeader(projectId, employeeNumber) {
    const project = projects.value.find((p) => p.id === projectId)
    return project && project.projectLeader.employeeNumber === employeeNumber
  }

  function getProjectsByDepartment(department) {
    return projects.value.filter((p) => p.department === department)
  }

  function getProjectsByStatus(status) {
    return projects.value.filter((p) => p.progressStatus === status)
  }

  return {
    projects,
    loading,
    error,
    projectCount,
    completedProjects,
    inProgressProjects,
    fetchProjects,
    getProjectById,
    addProject,
    updateProject,
    deleteProject,
    verifyProjectLeader,
    getProjectsByDepartment,
    getProjectsByStatus,
  }
})
