<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Admin Login
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Sign in to access the admin dashboard
        </p>
      </div>
      
      <Card>
        <template #content>
          <form @submit.prevent="handleLogin" class="space-y-6">
            <div class="form-field">
              <label class="form-label">
                Email Address *
              </label>
              <InputText
                v-model="form.email"
                type="email"
                placeholder="Enter your email"
                :class="{ 'p-invalid': errors.email }"
                @blur="validateField('email')"
              />
              <small v-if="errors.email" class="form-error">
                {{ errors.email }}
              </small>
            </div>

            <div class="form-field">
              <label class="form-label">
                Password *
              </label>
              <Password
                v-model="form.password"
                placeholder="Enter your password"
                :class="{ 'p-invalid': errors.password }"
                :feedback="false"
                toggleMask
                @blur="validateField('password')"
              />
              <small v-if="errors.password" class="form-error">
                {{ errors.password }}
              </small>
            </div>

            <div v-if="authStore.error" class="p-4 bg-red-50 border border-red-200 rounded-md">
              <div class="flex">
                <i class="pi pi-exclamation-triangle text-red-400 mr-2"></i>
                <div class="text-sm text-red-700">
                  {{ authStore.error }}
                </div>
              </div>
            </div>

            <Button 
              type="submit"
              label="Sign In"
              icon="pi pi-sign-in"
              class="w-full"
              :loading="authStore.loading"
              :disabled="!isFormValid"
            />
          </form>
        </template>
      </Card>

      <div class="text-center">
        <RouterLink 
          to="/projects" 
          class="text-blue-600 hover:text-blue-500 text-sm"
        >
          ← Back to Projects
        </RouterLink>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'primevue/usetoast'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const toast = useToast()

const form = ref({
  email: '',
  password: ''
})

const errors = ref({})

const isFormValid = computed(() => {
  return form.value.email && 
         form.value.password && 
         Object.keys(errors.value).length === 0
})

function validateField(field) {
  delete errors.value[field]
  
  switch (field) {
    case 'email':
      if (!form.value.email) {
        errors.value[field] = 'Email is required'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
        errors.value[field] = 'Please enter a valid email address'
      }
      break
    case 'password':
      if (!form.value.password) {
        errors.value[field] = 'Password is required'
      } else if (form.value.password.length < 6) {
        errors.value[field] = 'Password must be at least 6 characters'
      }
      break
  }
}

function validateAll() {
  errors.value = {}
  validateField('email')
  validateField('password')
  return Object.keys(errors.value).length === 0
}

async function handleLogin() {
  if (!validateAll()) return
  
  try {
    await authStore.login(form.value.email, form.value.password)
    
    toast.add({
      severity: 'success',
      summary: 'Login Successful',
      detail: 'Welcome to the admin dashboard',
      life: 3000
    })
    
    // Redirect to intended page or admin dashboard
    const redirectTo = route.query.redirect || '/admin'
    router.push(redirectTo)
    
  } catch (error) {
    // Error is handled by the store and displayed in the template
    console.error('Login failed:', error)
  }
}
</script>
