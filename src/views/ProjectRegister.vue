<template>
  <div class="max-w-4xl mx-auto">
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-color mb-2">Register New Project</h1>
      <p class="text-color-secondary">
        Fill out the form below to register a new EBP project in the central registry.
      </p>
    </div>

    <ProjectForm @submit="handleSubmit" @cancel="handleCancel" :loading="loading" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useToast } from 'primevue/usetoast'
import ProjectForm from '@/components/ProjectForm.vue'

const router = useRouter()
const projectsStore = useProjectsStore()
const toast = useToast()
const loading = ref(false)

async function handleSubmit(formData) {
  loading.value = true

  try {
    const projectId = await projectsStore.addProject(formData)

    toast.add({
      severity: 'success',
      summary: 'Project Registered',
      detail: `Project "${formData.projectTitle}" has been successfully registered with ID: ${projectId}`,
      life: 5000,
    })

    // Redirect to project list
    router.push('/projects')
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Registration Failed',
      detail: error.message || 'Failed to register project. Please try again.',
      life: 5000,
    })
  } finally {
    loading.value = false
  }
}

function handleCancel() {
  router.push('/projects')
}
</script>
