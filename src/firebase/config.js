import { initializeApp } from 'firebase/app'
import { getFirestore } from 'firebase/firestore'
import { getAuth } from 'firebase/auth'

// Firebase configuration
// TODO: Replace with your actual Firebase config from Firebase Console
const firebaseConfig = {
  apiKey: "AIzaSyD2BbJHZCiEr0FBzLhd6GpFqXRxmuP6SEs",
  authDomain: "ebp-project-registry.firebaseapp.com",
  projectId: "ebp-project-registry",
  storageBucket: "ebp-project-registry.firebasestorage.app",
  messagingSenderId: "83884410261",
  appId: "1:83884410261:web:2ee6ae76edf6329d23a29c"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig)

// Initialize Firebase services
export const db = getFirestore(app)
export const auth = getAuth(app)

// Development helper - log Firebase connection status
if (import.meta.env.DEV) {
  console.log('🔥 Firebase Configuration:')
  console.log('Project ID:', firebaseConfig.projectId)
  console.log('Auth Domain:', firebaseConfig.authDomain)

  if (firebaseConfig.projectId === 'demo-project-id') {
    console.warn('⚠️  Using demo Firebase config. Please set up your actual Firebase project!')
    console.log('📖 See FIREBASE_SETUP.md for instructions')
  } else {
    console.log('✅ Firebase configured successfully')
  }
}

export default app
