<template>
  <Card class="w-full">
    <template #title>
      <div class="flex align-items-center gap-2">
        <i class="pi pi-file-edit" style="color: var(--p-primary-color)"></i>
        <span>{{ isEdit ? 'Edit Project' : 'Register New Project' }}</span>
      </div>
    </template>

    <template #content>
      <form @submit.prevent="handleSubmit" class="flex flex-column gap-6">
        <!-- Department -->
        <div class="form-field">
          <label class="form-label"> Department * </label>
          <PrimeSelect
            v-model="form.department"
            :options="configStore.departments"
            placeholder="Select department"
            :class="{ 'p-invalid': errors.department }"
            @change="validateField('department')"
          />
          <small v-if="errors.department" class="form-error">
            {{ errors.department }}
          </small>
        </div>

        <!-- Project Title -->
        <div class="form-field">
          <label class="form-label"> Project Title * </label>
          <InputText
            v-model="form.projectTitle"
            placeholder="Enter project title (max 120 characters)"
            maxlength="120"
            :class="{ 'p-invalid': errors.projectTitle }"
            @blur="validateField('projectTitle')"
          />
          <div class="flex justify-content-between align-items-center mt-1">
            <small v-if="errors.projectTitle" class="form-error">
              {{ errors.projectTitle }}
            </small>
            <small class="text-color-secondary">
              {{ form.projectTitle.length }}/120 characters
            </small>
          </div>
        </div>

        <!-- Project Leader Section -->
        <div class="project-leader-section">
          <h3 class="text-lg font-medium mb-4">Project Leader</h3>

          <div class="grid md-grid-cols-3 gap-4">
            <!-- Leader Rank -->
            <div class="form-field">
              <label class="form-label"> Rank * </label>
              <PrimeSelect
                v-model="form.projectLeader.rank"
                :options="configStore.ranks"
                placeholder="Select rank"
                :class="{ 'p-invalid': errors['projectLeader.rank'] }"
                @change="validateField('projectLeader.rank')"
              />
              <small v-if="errors['projectLeader.rank']" class="form-error">
                {{ errors['projectLeader.rank'] }}
              </small>
            </div>

            <!-- Leader Full Name -->
            <div class="form-field">
              <label class="form-label"> Full Name * </label>
              <InputText
                v-model="form.projectLeader.fullName"
                placeholder="Enter full name"
                :class="{ 'p-invalid': errors['projectLeader.fullName'] }"
                @blur="validateField('projectLeader.fullName')"
              />
              <small v-if="errors['projectLeader.fullName']" class="form-error">
                {{ errors['projectLeader.fullName'] }}
              </small>
            </div>

            <!-- Leader Employee Number -->
            <div class="form-field">
              <label class="form-label"> Employee Number * </label>
              <InputText
                v-model="form.projectLeader.employeeNumber"
                placeholder="6-digit number"
                maxlength="6"
                :class="{ 'p-invalid': errors['projectLeader.employeeNumber'] }"
                @blur="validateField('projectLeader.employeeNumber')"
              />
              <small v-if="errors['projectLeader.employeeNumber']" class="form-error">
                {{ errors['projectLeader.employeeNumber'] }}
              </small>
            </div>
          </div>
        </div>

        <!-- Collaborators -->
        <CollaboratorManager
          v-model="form.collaborators"
          ref="collaboratorManager"
        />

        <!-- Progress Status -->
        <div class="form-field">
          <label class="form-label"> Progress Status * </label>
          <PrimeSelect
            v-model="form.progressStatus"
            :options="configStore.progressStatuses"
            placeholder="Select progress status"
            :class="{ 'p-invalid': errors.progressStatus }"
            @change="validateField('progressStatus')"
          />
          <small v-if="errors.progressStatus" class="form-error">
            {{ errors.progressStatus }}
          </small>
        </div>

        <!-- Project Type -->
        <div class="form-field">
          <label class="form-label"> Project Type * </label>
          <div class="grid md-grid-cols-4 gap-4">
            <div
              v-for="type in configStore.projectTypes"
              :key="type"
              class="flex align-items-center"
            >
              <RadioButton
                v-model="form.projectType"
                :inputId="type"
                :value="type"
                @change="validateField('projectType')"
              />
              <label :for="type" class="ml-2 text-sm">{{ type }}</label>
            </div>
          </div>
          <small v-if="errors.projectType" class="form-error">
            {{ errors.projectType }}
          </small>
        </div>

        <!-- Dates -->
        <div class="grid md-grid-cols-2 gap-4">
          <!-- Commencement Date -->
          <div class="form-field">
            <label class="form-label"> Commencement Date * </label>
            <DatePicker
              v-model="form.commencementDate"
              placeholder="Select date"
              :class="{ 'p-invalid': errors.commencementDate }"
              @date-select="validateField('commencementDate')"
              showIcon
            />
            <small v-if="errors.commencementDate" class="form-error">
              {{ errors.commencementDate }}
            </small>
          </div>

          <!-- Completion Date -->
          <div class="form-field">
            <label class="form-label"> Completion Date </label>
            <DatePicker
              v-model="form.completionDate"
              placeholder="Select date"
              :class="{ 'p-invalid': errors.completionDate }"
              @date-select="validateField('completionDate')"
              showIcon
            />
            <small v-if="errors.completionDate" class="form-error">
              {{ errors.completionDate }}
            </small>
          </div>
        </div>

        <!-- Project Details -->
        <div class="form-field">
          <label class="form-label"> Project Details </label>
          <Textarea
            v-model="form.projectDetails"
            placeholder="Enter project details (supports markdown)"
            rows="6"
            :class="{ 'p-invalid': errors.projectDetails }"
            @blur="validateField('projectDetails')"
          />
          <small v-if="errors.projectDetails" class="form-error">
            {{ errors.projectDetails }}
          </small>
          <small class="text-color-secondary">
            You can use markdown formatting for rich text
          </small>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <Button type="button" label="Cancel" icon="pi pi-times" text @click="$emit('cancel')" />
          <Button
            type="submit"
            :label="isEdit ? 'Update Project' : 'Register Project'"
            :icon="isEdit ? 'pi pi-check' : 'pi pi-plus'"
            :loading="loading"
          />
        </div>
      </form>
    </template>
  </Card>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { useConfigStore } from '@/stores/config'
import CollaboratorManager from './CollaboratorManager.vue'

const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({}),
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['submit', 'cancel'])

const configStore = useConfigStore()
const collaboratorManager = ref(null)

// Form data
const form = reactive({
  department: '',
  projectTitle: '',
  projectLeader: {
    rank: '',
    fullName: '',
    employeeNumber: '',
  },
  collaborators: [],
  progressStatus: 'Not Started',
  projectType: '',
  commencementDate: null,
  completionDate: null,
  projectDetails: '',
})

// Form errors
const errors = ref({})

// Collaborator management is now handled by CollaboratorManager component via v-model


// Helper function to convert Firebase Timestamp to Date
function convertFirebaseDate(date) {
  if (!date) return null

  try {
    if (date.toDate && typeof date.toDate === 'function') {
      // Firebase Timestamp
      return date.toDate()
    } else if (date.seconds) {
      // Firebase Timestamp object
      return new Date(date.seconds * 1000)
    } else if (date instanceof Date) {
      // Already a Date object
      return date
    } else {
      // Try to parse as regular date
      const parsed = new Date(date)
      return isNaN(parsed.getTime()) ? null : parsed
    }
  } catch (error) {
    console.warn('Date conversion error:', error, date)
    return null
  }
}

const currentProjectId = ref(null)
console.log('currentProjectId: ', currentProjectId.value)

// Initialize form with data if editing
// function initializeForm() {
//   if (props.isEdit && props.initialData && props.initialData.id !== currentProjectId.value) {
//     currentProjectId.value = props.initialData.id
//     console.log('currentProjectId after fetching data: ', currentProjectId.value)
//     Object.assign(form, {
//       ...props.initialData,
//       commencementDate: convertFirebaseDate(props.initialData.commencementDate),
//       completionDate: convertFirebaseDate(props.initialData.completionDate),
//     })
//   } else {
//     console.log('Updated')
//   }
// }

// Update form when projectData changes
watch(() => props.initialData, (newData) => {
  // Only update if the ID has changed
  if (newData && newData.id !== currentProjectId.value) {
    currentProjectId.value = newData.id;

    // Update form fields
    Object.keys(form).forEach(key => {
      if (key === 'commencementDate' || key === 'completionDate') {
        form[key] = convertFirebaseDate(newData[key]) ?? form[key];
      } else {
        form[key] = newData[key] ?? form[key];
      }
    });
  } else {
    console.log('Watcher triggered');
  }
}, { immediate: true, deep: true });

// Watch for changes in initialData
// watch(() => props.initialData, (newVal) => {
//   if (props.isEdit && newVal) {
//     initializeForm()
//   }
// }, { deep: true })

// Initialize form on mount
// onMounted(() => {
//   initializeForm()
// })


function validateField(field) {
  // Clear previous error
  delete errors.value[field]

  switch (field) {
    case 'department':
      if (!form.department) {
        errors.value[field] = 'Department is required'
      }
      break
    case 'projectTitle':
      if (!form.projectTitle || form.projectTitle.trim().length < 5) {
        errors.value[field] = 'Project title is required (minimum 5 characters)'
      }
      break
    case 'projectLeader.rank':
      if (!form.projectLeader.rank) {
        errors.value[field] = 'Project leader rank is required'
      }
      break
    case 'projectLeader.fullName':
      if (!form.projectLeader.fullName || form.projectLeader.fullName.trim().length < 2) {
        errors.value[field] = 'Project leader full name is required'
      }
      break
    case 'projectLeader.employeeNumber':
      if (!form.projectLeader.employeeNumber) {
        errors.value[field] = 'Project leader employee number is required'
      } else if (!/^\d{6}$/.test(form.projectLeader.employeeNumber)) {
        errors.value[field] = 'Employee number must be exactly 6 digits'
      }
      break
    case 'progressStatus':
      if (!form.progressStatus) {
        errors.value[field] = 'Progress status is required'
      }
      break
    case 'projectType':
      if (!form.projectType) {
        errors.value[field] = 'Project type is required'
      }
      break
    case 'commencementDate':
      if (!form.commencementDate) {
        errors.value[field] = 'Commencement date is required'
      }
      break
    case 'completionDate':
      if (
        form.completionDate &&
        form.commencementDate &&
        new Date(form.completionDate) <= new Date(form.commencementDate)
      ) {
        errors.value[field] = 'Completion date must be after commencement date'
      }
      break
  }
}

function validateAll() {
  errors.value = {}

  // Validate all fields
  const fieldsToValidate = [
    'department',
    'projectTitle',
    'projectLeader.rank',
    'projectLeader.fullName',
    'projectLeader.employeeNumber',
    'progressStatus',
    'projectType',
    'commencementDate',
    'completionDate',
  ]

  fieldsToValidate.forEach(validateField)

  // Validate collaborators
  const collaboratorsValid = collaboratorManager.value?.validateAll() ?? true

  return Object.keys(errors.value).length === 0 && collaboratorsValid
}

function handleSubmit() {
  if (validateAll()) {
    emit('submit', { ...form })
  }
}
</script>
