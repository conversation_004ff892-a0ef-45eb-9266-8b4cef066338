<template>
  <div v-if="isDevelopment" class="fixed bottom-4 right-4 z-50">
    <Card class="w-80 shadow-lg">
      <template #title>
        <div class="flex items-center space-x-2">
          <i class="pi pi-cog text-blue-600"></i>
          <span class="text-sm">Dev Tools</span>
        </div>
      </template>

      <template #content>
        <div class="space-y-3">
          <div class="text-xs text-gray-600">Development utilities for testing</div>

          <Button
            @click="addSampleData"
            icon="pi pi-database"
            label="Add Sample Projects"
            class="w-full p-button-sm p-button-outlined"
            :loading="loading"
            :disabled="projectsStore.projects.length > 0"
          />

          <Button
            @click="clearAllData"
            icon="pi pi-trash"
            label="Clear All Projects"
            class="w-full p-button-sm p-button-outlined p-button-danger"
            :loading="loading"
            :disabled="projectsStore.projects.length === 0"
          />

          <div class="text-xs text-gray-500">Projects: {{ projectsStore.projects.length }}</div>

          <Button
            @click="visible = false"
            icon="pi pi-times"
            label="Hide"
            class="w-full p-button-sm p-button-text"
          />
        </div>
      </template>
    </Card>
  </div>

  <!-- Floating toggle button when hidden -->
  <Button
    v-if="isDevelopment && !visible"
    @click="visible = true"
    icon="pi pi-cog"
    class="fixed bottom-4 right-4 z-50 p-button-rounded p-button-sm"
    v-tooltip="'Dev Tools'"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import { useProjectsStore } from '@/stores/projects'
import { useToast } from 'primevue/usetoast'
import { addSampleData as seedSampleData } from '@/utils/sampleData'

const projectsStore = useProjectsStore()
const toast = useToast()

const visible = ref(true)
const loading = ref(false)

const isDevelopment = computed(() => {
  return import.meta.env.DEV || import.meta.env.MODE === 'development'
})

async function addSampleData() {
  loading.value = true

  try {
    const success = await seedSampleData(projectsStore)

    if (success) {
      toast.add({
        severity: 'success',
        summary: 'Sample Data Added',
        detail: 'Sample projects have been added to the database',
        life: 3000,
      })
    } else {
      throw new Error('Failed to add sample data')
    }
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to add sample data: ' + error.message,
      life: 5000,
    })
  } finally {
    loading.value = false
  }
}

async function clearAllData() {
  if (!confirm('Are you sure you want to delete ALL projects? This cannot be undone.')) {
    return
  }

  loading.value = true

  try {
    const projects = [...projectsStore.projects]

    for (const project of projects) {
      await projectsStore.deleteProject(project.id)
    }

    toast.add({
      severity: 'success',
      summary: 'Data Cleared',
      detail: 'All projects have been deleted',
      life: 3000,
    })
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to clear data: ' + error.message,
      life: 5000,
    })
  } finally {
    loading.value = false
  }
}
</script>
