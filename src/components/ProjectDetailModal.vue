<template>
  <Dialog
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    modal
    :header="project?.projectTitle || 'Project Details'"
    :style="{ width: '90vw', maxWidth: '800px' }"
    :closable="true"
  >
    <div v-if="project" class="project-detail-content">
      <!-- Project Header -->
      <div class="project-header mb-4">
        <div class="flex justify-content-between align-items-start mb-3">
          <div>
            <h2 class="text-2xl font-bold text-color mb-2">{{ project.projectTitle }}</h2>
            <div class="flex align-items-center gap-2 mb-2">
              <Tag
                :value="project.progressStatus"
                :severity="getStatusSeverity(project.progressStatus)"
              />
              <span class="text-color-secondary">•</span>
              <span class="text-color-secondary">{{ project.department }}</span>
            </div>
            <p class="text-color-secondary text-sm">Project ID: {{ project.id }}</p>
          </div>
        </div>
      </div>

      <!-- Project Description -->
      <div class="mb-4">
        <h3 class="text-lg font-semibold text-color mb-2">Description</h3>
        <p class="text-color line-height-3">
          {{ project.projectDescription || 'No description provided.' }}
        </p>
      </div>

      <!-- Project Leader -->
      <div class="mb-4">
        <h3 class="text-lg font-semibold text-color mb-2">Project Leader</h3>
        <Card class="project-leader-card">
          <template #content>
            <div class="flex align-items-center gap-3">
              <div class="leader-avatar">
                <i class="pi pi-user text-2xl text-primary"></i>
              </div>
              <div>
                <div class="font-semibold text-color">{{ project.projectLeader?.fullName }}</div>
                <div class="text-color-secondary">{{ project.projectLeader?.rank }}</div>
                <div class="text-color-secondary text-sm">
                  Employee #{{ project.projectLeader?.employeeNumber }}
                </div>
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Collaborators -->
      <div v-if="project.collaborators && project.collaborators.length > 0" class="mb-4">
        <h3 class="text-lg font-semibold text-color mb-2">
          Collaborators ({{ project.collaborators.length }})
        </h3>
        <div class="grid md-grid-cols-2 gap-3">
          <Card
            v-for="(collaborator, index) in project.collaborators"
            :key="index"
            class="collaborator-card"
          >
            <template #content>
              <div class="flex align-items-center gap-2">
                <i class="pi pi-users text-primary"></i>
                <div>
                  <div class="font-medium text-color">{{ collaborator.fullName }}</div>
                  <div class="text-color-secondary text-sm">{{ collaborator.rank }}</div>
                  <div class="text-color-secondary text-xs">
                    Emp #{{ collaborator.employeeNumber }}
                  </div>
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>

      <!-- Project Dates -->
      <div class="mb-4">
        <h3 class="text-lg font-semibold text-color mb-2">Timeline</h3>
        <div class="grid md-grid-cols-2 gap-4">
          <div class="timeline-item">
            <div class="flex align-items-center gap-2 mb-1">
              <i class="pi pi-calendar text-primary"></i>
              <span class="font-medium text-color">Commencement Date</span>
            </div>
            <p class="text-color-secondary ml-4">
              {{ formatDate(project.commencementDate) }}
            </p>
          </div>
          <div class="timeline-item">
            <div class="flex align-items-center gap-2 mb-1">
              <i class="pi pi-calendar-times text-primary"></i>
              <span class="font-medium text-color">Completion Date</span>
            </div>
            <p class="text-color-secondary ml-4">
              {{ project.completionDate ? formatDate(project.completionDate) : 'Not specified' }}
            </p>
          </div>
        </div>
      </div>

      <!-- Project Metadata -->
      <div class="project-metadata">
        <h3 class="text-lg font-semibold text-color mb-2">Project Information</h3>
        <div class="grid md-grid-cols-2 gap-4">
          <div class="metadata-item">
            <span class="font-medium text-color">Created:</span>
            <span class="text-color-secondary ml-2">{{ formatDate(project.createdAt) }}</span>
          </div>
          <div class="metadata-item">
            <span class="font-medium text-color">Last Updated:</span>
            <span class="text-color-secondary ml-2">{{ formatDate(project.updatedAt) }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-content-end gap-2">
        <Button label="Close" text @click="$emit('update:visible', false)" />
        <Button
          v-if="canEdit"
          label="Edit Project"
          icon="pi pi-pencil"
          @click="$emit('edit-project', project)"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { format } from 'date-fns'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  project: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:visible', 'edit-project'])

// Simplified - always show edit button for now
const canEdit = true

function formatDate(date) {
  if (!date) return 'N/A'
  try {
    let dateObj
    if (date.toDate && typeof date.toDate === 'function') {
      // Firebase Timestamp
      dateObj = date.toDate()
    } else if (date.seconds) {
      // Firebase Timestamp object
      dateObj = new Date(date.seconds * 1000)
    } else {
      // Regular date
      dateObj = new Date(date)
    }

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return 'Invalid date'
    }

    return format(dateObj, 'MMM dd, yyyy')
  } catch (error) {
    console.warn('Date formatting error:', error, date)
    return 'Invalid date'
  }
}

function getStatusSeverity(status) {
  const severityMap = {
    Planning: 'info',
    'In Progress': 'warning',
    Completed: 'success',
    'On Hold': 'secondary',
    Cancelled: 'danger',
  }
  return severityMap[status] || 'info'
}
</script>

<style scoped>
.project-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.project-leader-card {
  background: var(--p-surface-50);
  border: 1px solid var(--p-surface-200);
}

.leader-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--p-primary-50);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--p-primary-200);
}

.collaborator-card {
  background: var(--p-surface-0);
  border: 1px solid var(--p-surface-200);
  transition: all 0.2s ease;
}

.collaborator-card:hover {
  border-color: var(--p-primary-200);
  box-shadow: var(--p-shadow-2);
}

.timeline-item {
  padding: 1rem;
  background: var(--p-surface-50);
  border-radius: var(--p-border-radius);
  border: 1px solid var(--p-surface-200);
}

.metadata-item {
  padding: 0.75rem;
  background: var(--p-surface-0);
  border-radius: var(--p-border-radius);
  border: 1px solid var(--p-surface-200);
}

.project-header {
  border-bottom: 1px solid var(--p-surface-200);
  padding-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .project-detail-content {
    max-height: 60vh;
  }

  .grid.md-grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>
