<template>
  <div v-if="showStatus" class="firebase-status">
    <Card>
      <template #title>
        <div class="flex align-items-center gap-2">
          <i :class="statusIcon" :style="{ color: statusColor }"></i>
          Firebase Connection Status
        </div>
      </template>
      <template #content>
        <div class="flex flex-column gap-3">
          <div>
            <strong>Project ID:</strong> {{ projectId }}
          </div>
          <div>
            <strong>Status:</strong> 
            <span :style="{ color: statusColor, fontWeight: 'bold' }">
              {{ statusMessage }}
            </span>
          </div>
          <div v-if="!isConfigured" class="firebase-warning">
            <p>⚠️ Firebase is not properly configured.</p>
            <p>Please follow the setup instructions in <code>FIREBASE_SETUP.md</code></p>
            <Button 
              label="Hide This Message" 
              text 
              size="small"
              @click="hideStatus"
            />
          </div>
          <div v-else-if="authError" class="firebase-error">
            <p>❌ Authentication Error:</p>
            <code>{{ authError }}</code>
          </div>
          <div v-else-if="dbError" class="firebase-error">
            <p>❌ Database Error:</p>
            <code>{{ dbError }}</code>
          </div>
        </div>
      </template>
    </Card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useProjectsStore } from '@/stores/projects'

const authStore = useAuthStore()
const projectsStore = useProjectsStore()

const showStatus = ref(false)
const authError = ref(null)
const dbError = ref(null)

const projectId = import.meta.env.VITE_FIREBASE_PROJECT_ID || 'demo-project-id'

const isConfigured = computed(() => {
  return projectId !== 'demo-project-id' && 
         import.meta.env.VITE_FIREBASE_API_KEY &&
         import.meta.env.VITE_FIREBASE_API_KEY !== 'demo-api-key'
})

const statusIcon = computed(() => {
  if (!isConfigured.value) return 'pi pi-exclamation-triangle'
  if (authError.value || dbError.value) return 'pi pi-times-circle'
  return 'pi pi-check-circle'
})

const statusColor = computed(() => {
  if (!isConfigured.value) return '#f59e0b'
  if (authError.value || dbError.value) return '#ef4444'
  return '#10b981'
})

const statusMessage = computed(() => {
  if (!isConfigured.value) return 'Not Configured'
  if (authError.value || dbError.value) return 'Connection Error'
  return 'Connected'
})

function hideStatus() {
  showStatus.value = false
  localStorage.setItem('hideFirebaseStatus', 'true')
}

onMounted(async () => {
  // Only show in development and if not hidden
  if (import.meta.env.DEV && !localStorage.getItem('hideFirebaseStatus')) {
    showStatus.value = true
    
    // Test Firebase connection
    try {
      await projectsStore.fetchProjects()
    } catch (error) {
      dbError.value = error.message
    }
    
    // Check auth errors
    if (authStore.error) {
      authError.value = authStore.error
    }
  }
})
</script>

<style scoped>
.firebase-status {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  max-width: 400px;
  box-shadow: var(--p-shadow-4);
}

.firebase-warning {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 0.5rem;
  padding: 1rem;
  color: #92400e;
}

.firebase-error {
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 0.5rem;
  padding: 1rem;
  color: #dc2626;
}

.firebase-error code {
  background-color: #fecaca;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .firebase-status {
    position: relative;
    top: 0;
    right: 0;
    margin: 1rem;
    max-width: none;
  }
}
</style>
