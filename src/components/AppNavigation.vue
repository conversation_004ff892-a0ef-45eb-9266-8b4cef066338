<template>
  <nav class="nav-container">
    <div class="container">
      <div class="flex justify-content-between align-items-center p-4">
        <!-- Logo/Title -->
        <div class="nav-brand">
          <div class="nav-icon">
            <i class="pi pi-heart"></i>
          </div>
          <h1 class="nav-title">EBP Project Central Registry</h1>
        </div>

        <!-- Desktop Navigation -->
        <div class="desktop-only flex align-items-center gap-4">
          <RouterLink
            to="/projects"
            class="nav-link"
            :class="{ active: $route.name === 'projects' }"
          >
            <i class="pi pi-list mr-2"></i>
            Projects
          </RouterLink>

          <RouterLink
            to="/register"
            class="nav-link"
            :class="{ active: $route.name === 'register' }"
          >
            <i class="pi pi-plus mr-2"></i>
            Register Project
          </RouterLink>

          <RouterLink
            v-if="authStore.isAuthenticated"
            to="/admin"
            class="nav-link"
            :class="{ active: $route.name === 'admin' }"
          >
            <i class="pi pi-cog mr-2"></i>
            Admin
          </RouterLink>

          <Button
            v-if="authStore.isAuthenticated"
            @click="handleLogout"
            icon="pi pi-sign-out"
            label="Logout"
            text
            size="small"
          />

          <RouterLink
            v-else
            to="/login"
            class="nav-link"
            :class="{ active: $route.name === 'login' }"
          >
            <i class="pi pi-sign-in mr-2"></i>
            Admin Login
          </RouterLink>
        </div>

        <!-- Mobile Menu Button -->
        <Button
          @click="mobileMenuOpen = !mobileMenuOpen"
          icon="pi pi-bars"
          text
          class="mobile-only"
        />
      </div>

      <!-- Mobile Navigation -->
      <div v-if="mobileMenuOpen" class="mobile-only mobile-nav-container">
        <div class="flex flex-column gap-2 p-4">
          <RouterLink to="/projects" class="mobile-nav-link" @click="mobileMenuOpen = false">
            <i class="pi pi-list mr-2"></i>
            Projects
          </RouterLink>

          <RouterLink to="/register" class="mobile-nav-link" @click="mobileMenuOpen = false">
            <i class="pi pi-plus mr-2"></i>
            Register Project
          </RouterLink>

          <RouterLink
            v-if="authStore.isAuthenticated"
            to="/admin"
            class="mobile-nav-link"
            @click="mobileMenuOpen = false"
          >
            <i class="pi pi-cog mr-2"></i>
            Admin
          </RouterLink>

          <Button
            v-if="authStore.isAuthenticated"
            @click="handleLogout"
            icon="pi pi-sign-out"
            label="Logout"
            text
            class="mobile-nav-button"
          />

          <RouterLink v-else to="/login" class="mobile-nav-link" @click="mobileMenuOpen = false">
            <i class="pi pi-sign-in mr-2"></i>
            Admin Login
          </RouterLink>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'primevue/usetoast'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()
const mobileMenuOpen = ref(false)

async function handleLogout() {
  try {
    await authStore.logout()
    toast.add({
      severity: 'success',
      summary: 'Logged Out',
      detail: 'You have been successfully logged out',
      life: 3000,
    })
    router.push('/projects')
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Logout Failed',
      detail: error.message,
      life: 5000,
    })
  }
  mobileMenuOpen.value = false
}
</script>

<style scoped>
/* Navigation Links */
.nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: var(--p-text-color);
  border-radius: var(--p-border-radius);
  transition: all 0.2s ease-in-out;
  font-weight: 500;
  text-decoration: none;
}

.nav-link:hover {
  color: var(--p-primary-color);
  background-color: var(--p-primary-50);
  transform: translateY(-1px);
}

.nav-link.active {
  color: var(--p-primary-color);
  background-color: var(--p-primary-100);
  box-shadow: var(--p-shadow-2);
}

/* Mobile Navigation */
.mobile-nav-container {
  border-top: 1px solid var(--p-surface-border);
  background-color: var(--p-surface-card);
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--p-text-color);
  border-radius: var(--p-border-radius);
  transition: all 0.2s ease-in-out;
  font-weight: 500;
  border-left: 3px solid transparent;
  text-decoration: none;
}

.mobile-nav-link:hover {
  color: var(--p-primary-color);
  background-color: var(--p-primary-50);
  border-left-color: var(--p-primary-color);
}

.mobile-nav-button {
  justify-content: flex-start;
  text-align: left;
}
</style>
