<template>
  <div class="flex items-center justify-center" :class="containerClass">
    <div class="loading-spinner" :class="spinnerClass"></div>
    <span v-if="message" class="ml-3 text-gray-600">{{ message }}</span>
  </div>
</template>

<script setup>
const props = defineProps({
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  message: {
    type: String,
    default: ''
  },
  fullScreen: {
    type: Boolean,
    default: false
  }
})

const containerClass = computed(() => {
  if (props.fullScreen) {
    return 'fixed inset-0 bg-white bg-opacity-75 z-50'
  }
  return 'py-8'
})

const spinnerClass = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'h-4 w-4'
    case 'lg':
      return 'h-12 w-12'
    default:
      return 'h-8 w-8'
  }
})
</script>

<script>
import { computed } from 'vue'
export default {
  name: 'LoadingSpinner'
}
</script>
