<template>
  <div v-if="error" class="error-container" :class="containerClass">
    <div class="flex items-start">
      <div class="flex-shrink-0">
        <i class="pi pi-exclamation-triangle text-red-400" :class="iconClass"></i>
      </div>
      <div class="ml-3 flex-1">
        <h3 v-if="title" class="text-sm font-medium text-red-800">
          {{ title }}
        </h3>
        <div class="text-sm text-red-700" :class="{ 'mt-1': title }">
          {{ error }}
        </div>
        <div v-if="showRetry" class="mt-3">
          <Button 
            @click="$emit('retry')"
            label="Try Again"
            icon="pi pi-refresh"
            class="p-button-sm p-button-outlined"
            severity="danger"
          />
        </div>
      </div>
      <div v-if="dismissible" class="ml-auto pl-3">
        <button 
          @click="$emit('dismiss')"
          class="inline-flex text-red-400 hover:text-red-600"
        >
          <i class="pi pi-times"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  error: {
    type: String,
    required: true
  },
  title: {
    type: String,
    default: 'Error'
  },
  variant: {
    type: String,
    default: 'error',
    validator: (value) => ['error', 'warning', 'info'].includes(value)
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  showRetry: {
    type: Boolean,
    default: false
  },
  dismissible: {
    type: Boolean,
    default: false
  }
})

defineEmits(['retry', 'dismiss'])

const containerClass = computed(() => {
  const baseClasses = 'rounded-md p-4 border'
  
  const variantClasses = {
    error: 'bg-red-50 border-red-200',
    warning: 'bg-yellow-50 border-yellow-200',
    info: 'bg-blue-50 border-blue-200'
  }
  
  const sizeClasses = {
    sm: 'p-2',
    md: 'p-4',
    lg: 'p-6'
  }
  
  return `${baseClasses} ${variantClasses[props.variant]} ${sizeClasses[props.size]}`
})

const iconClass = computed(() => {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }
  
  return sizeClasses[props.size]
})
</script>
