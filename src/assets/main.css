/* PrimeVue Aura Theme - Clean CSS (No Tailwind) */

/* Force Light Theme - Override Dark Mode */
:root {
  /* Ensure light theme colors */
  --p-surface-ground: #ffffff;
  --p-surface-card: #ffffff;
  --p-text-color: #1f2937;
  --p-text-color-secondary: #6b7280;
  --p-text-muted-color: #9ca3af;
  --p-surface-border: #e5e7eb;
  --p-primary-color: #3b82f6;
  --p-primary-50: #eff6ff;
  --p-primary-100: #dbeafe;
  --p-primary-500: #3b82f6;
  --p-primary-600: #2563eb;
}

/* Force all cards and forms to have light backgrounds */
.p-card {
  background: #ffffff !important;
  color: #1f2937 !important;
  border: 1px solid #e5e7eb !important;
}

.p-card .p-card-title {
  color: #1f2937 !important;
}

.p-card .p-card-content {
  color: #1f2937 !important;
}

/* Force form inputs to be light */
.p-inputtext,
.p-select,
.p-datepicker input,
.p-textarea {
  background: #ffffff !important;
  color: #1f2937 !important;
  border: 1px solid #d1d5db !important;
}

.p-select-label {
  color: #1f2937 !important;
}

/* Force labels to be dark */
.form-label,
label {
  color: #1f2937 !important;
}

/* Additional form element overrides */
.p-radiobutton + label {
  color: #1f2937 !important;
}

.p-select-overlay {
  background: #ffffff !important;
  border: 1px solid #d1d5db !important;
}

.p-select-overlay .p-select-option {
  color: #1f2937 !important;
}

.p-select-overlay .p-select-option:hover {
  background: #f3f4f6 !important;
  color: #1f2937 !important;
}

.p-datepicker-panel {
  background: #ffffff !important;
  border: 1px solid #d1d5db !important;
}

/* Force all text in cards to be dark */
.p-card * {
  color: #1f2937 !important;
}

/* Override any remaining dark text */
h1,
h2,
h3,
h4,
h5,
h6,
p,
span,
div {
  color: inherit;
}

/* Ensure placeholder text is visible */
.p-inputtext::placeholder,
.p-textarea::placeholder {
  color: #9ca3af !important;
}

/* Global Reset */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: var(--p-font-family);
  background-color: var(--p-surface-ground);
  color: var(--p-text-color);
  line-height: 1.6;
}

#app {
  min-height: 100vh;
}

/* Basic Layout Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-center {
  justify-content: center;
}

.align-items-center {
  align-items: center;
}

.grid {
  display: grid;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

/* Spacing */
.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.m-0 {
  margin: 0;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-4 {
  margin-left: 1rem;
}

/* Typography */
.text-center {
  text-align: center;
}

.font-bold {
  font-weight: bold;
}

.font-semibold {
  font-weight: 600;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

/* Text Color Utilities */
.text-color {
  color: #1f2937 !important;
}

.text-color-secondary {
  color: #6b7280 !important;
}

/* Width */
.w-full {
  width: 100%;
}

.max-w-4xl {
  max-width: 56rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Form Styles */
.form-field {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937 !important;
  margin-bottom: 0.5rem;
}

.form-error {
  color: var(--p-red-500);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Loading Spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border: 2px solid var(--p-surface-200);
  border-bottom: 2px solid var(--p-primary-color);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Classes */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  .hidden-mobile {
    display: none !important;
  }

  .mobile-only {
    display: block !important;
  }

  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .mobile-flex-col {
    flex-direction: column !important;
  }

  .mobile-gap-2 {
    gap: 0.5rem !important;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none !important;
  }

  .desktop-only {
    display: flex !important;
  }
}

@media (min-width: 768px) {
  .md-flex {
    display: flex !important;
  }

  .md-hidden {
    display: none !important;
  }

  .md-grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .md-grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Navigation Styles */
.nav-container {
  background: var(--p-surface-card);
  border-bottom: 1px solid var(--p-surface-border);
  box-shadow: var(--p-shadow-2);
}

/* Ensure desktop navigation is horizontal */
@media (min-width: 769px) {
  .desktop-only {
    display: flex !important;
    align-items: center;
    gap: 1rem;
  }
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.nav-icon {
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, var(--p-primary-500), var(--p-primary-600));
  border-radius: var(--p-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.nav-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--p-text-color);
  margin: 0;
}

/* Card Enhancements */
.stats-card {
  background: linear-gradient(135deg, var(--p-surface-card), var(--p-surface-50));
  border: 1px solid var(--p-surface-border);
  border-radius: var(--p-border-radius);
  transition: all 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--p-shadow-4);
}

.stats-icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--p-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

/* Status Badge Styles */
.status-completed {
  background-color: var(--p-green-100);
  color: var(--p-green-900);
  border: 1px solid var(--p-green-200);
}

.status-in-progress {
  background-color: var(--p-blue-100);
  color: var(--p-blue-900);
  border: 1px solid var(--p-blue-200);
}

.status-not-started {
  background-color: var(--p-surface-100);
  color: var(--p-surface-700);
  border: 1px solid var(--p-surface-200);
}

/* Project Form Styles */
.project-leader-section {
  background-color: #f9fafb !important;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb !important;
}

.project-leader-section * {
  color: #1f2937 !important;
}

.form-actions {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 0.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--p-surface-border);
}

@media (min-width: 640px) {
  .form-actions {
    flex-direction: row;
    gap: 0.75rem;
  }
}
