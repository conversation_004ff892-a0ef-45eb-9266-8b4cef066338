# EBP Project Central Registry

A comprehensive web application for managing Evidence-Based Practice (EBP) projects in healthcare organizations. Built with Vue 3, PrimeVue, Tailwind CSS, and Firebase.

## Features

### 🏥 Project Management

- **Project Registration**: Complete form with validation for new project registration
- **Project Listing**: Sortable, filterable DataTable with pagination
- **Project Editing**: Secure editing with project leader verification
- **Collaborator Management**: Dynamic addition/removal of project collaborators
- **Leadership Transfer**: Secure transfer of project leadership

### 🔐 Security Model

- **Employee Verification**: Actions require project leader's employee number verification
- **Admin Authentication**: Firebase Auth for admin-only features
- **Public Read Access**: All projects visible to organization members
- **Secure Mutations**: Write operations protected by verification workflow

### 📱 Responsive Design

- **Mobile-First**: Optimized for mobile devices
- **Tablet Support**: Enhanced layouts for tablet viewing
- **Desktop Experience**: Full-featured desktop interface
- **PrimeVue Components**: Professional UI components throughout

### ⚡ Performance

- **Lazy Loading**: Components loaded on demand
- **Optimized Queries**: Efficient Firestore queries with indexing
- **State Management**: Pinia for efficient state handling

## Tech Stack

- **Frontend**: Vue 3 (Composition API)
- **UI Framework**: PrimeVue with Tailwind CSS
- **State Management**: Pinia
- **Routing**: Vue Router
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Hosting**: Firebase Hosting
- **Build Tool**: Vite

## Project Setup

### Prerequisites

- Node.js 16+
- Firebase account
- Firebase CLI (for deployment)

### Installation

1. **Clone and install dependencies**

```bash
npm install
```

2. **Firebase Setup (Required)**

   **Option A: Quick Setup (Recommended)**

   ```bash
   npm run setup:firebase
   ```

   Follow the prompts to enter your Firebase configuration.

   **Option B: Manual Setup**

   - Create a Firebase project at https://console.firebase.google.com
   - Enable Firestore Database and Authentication (Email/Password)
   - Copy `.env.example` to `.env.local`
   - Fill in your Firebase config values in `.env.local`

   **📖 For detailed instructions, see [FIREBASE_SETUP.md](./FIREBASE_SETUP.md)**

3. **Start Development Server**

```bash
npm run dev
```

4. **Create Admin User**
   - Go to Firebase Console > Authentication > Users
   - Add user with email: `<EMAIL>`
   - Use this account to login at `/login`

### Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint with ESLint
npm run lint
```

### Firebase Deployment

1. **Install Firebase CLI**

```bash
npm install -g firebase-tools
```

2. **Login and initialize**

```bash
firebase login
firebase init
```

3. **Deploy Firestore rules and indexes**

```bash
firebase deploy --only firestore
```

4. **Build and deploy hosting**

```bash
npm run build
firebase deploy --only hosting
```

## Application Structure

### Pages

- **Project List** (`/projects`) - Main dashboard with project table
- **Project Register** (`/register`) - New project registration form
- **Admin Dashboard** (`/admin`) - Protected admin interface
- **Login** (`/login`) - Admin authentication

### Key Components

- **ProjectForm** - Reusable form for project creation/editing
- **CollaboratorManager** - Dynamic collaborator management
- **VerificationModal** - Employee number verification
- **AppNavigation** - Responsive navigation component

### Security Features

- Employee number verification for project mutations
- Firebase Auth for admin access only
- Firestore security rules for data protection
- Input validation and sanitization

## Configuration

### Dropdown Options

Managed through the admin dashboard:

- Departments
- Staff Ranks
- Progress Statuses
- Project Types

### Firestore Collections

- `projects` - Project documents
- `config` - Configuration settings
- `admins` - Admin user management

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
