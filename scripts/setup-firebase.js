#!/usr/bin/env node

/**
 * Firebase Setup Helper Script
 * This script helps you set up Firebase configuration for the EBP Project Registry
 */

import { readFileSync, writeFileSync, existsSync } from 'fs'
import { createInterface } from 'readline'

const rl = createInterface({
  input: process.stdin,
  output: process.stdout
})

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

async function main() {
  console.log('🔥 Firebase Setup Helper for EBP Project Registry\n')
  
  // Check if .env.local already exists
  if (existsSync('.env.local')) {
    const overwrite = await question('⚠️  .env.local already exists. Overwrite? (y/N): ')
    if (overwrite.toLowerCase() !== 'y') {
      console.log('Setup cancelled.')
      rl.close()
      return
    }
  }
  
  console.log('Please provide your Firebase configuration values.')
  console.log('You can find these in Firebase Console > Project Settings > General > Your apps\n')
  
  const apiKey = await question('Firebase API Key: ')
  const authDomain = await question('Auth Domain (project-id.firebaseapp.com): ')
  const projectId = await question('Project ID: ')
  const storageBucket = await question('Storage Bucket (project-id.appspot.com): ')
  const messagingSenderId = await question('Messaging Sender ID: ')
  const appId = await question('App ID: ')
  
  const envContent = `# Firebase Configuration
# Generated by setup-firebase.js on ${new Date().toISOString()}

VITE_FIREBASE_API_KEY=${apiKey}
VITE_FIREBASE_AUTH_DOMAIN=${authDomain}
VITE_FIREBASE_PROJECT_ID=${projectId}
VITE_FIREBASE_STORAGE_BUCKET=${storageBucket}
VITE_FIREBASE_MESSAGING_SENDER_ID=${messagingSenderId}
VITE_FIREBASE_APP_ID=${appId}
`
  
  try {
    writeFileSync('.env.local', envContent)
    console.log('\n✅ Firebase configuration saved to .env.local')
    console.log('\n🚀 Next steps:')
    console.log('1. Restart your development server: npm run dev')
    console.log('2. Create an admin user in Firebase Console > Authentication')
    console.log('3. Test the application at http://localhost:5173')
    console.log('\n📖 For detailed instructions, see FIREBASE_SETUP.md')
  } catch (error) {
    console.error('❌ Error saving configuration:', error.message)
  }
  
  rl.close()
}

main().catch(console.error)
